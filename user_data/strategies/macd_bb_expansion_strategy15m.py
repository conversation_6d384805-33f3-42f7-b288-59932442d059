# --- 请将本文件保存为 user_data/strategies/macd_bb_expansion_strategy.py ---

import freqtrade.vendor.qtpylib.indicators as qtpylib
import talib.abstract as ta
from freqtrade.strategy import DecimalParameter, IntParameter, IStrategy
from pandas import DataFrame


class MacdBBExpansionStrategy(IStrategy):
    """
    一个带有【真实布林带扩张】过滤的动量突破策略

    作者: 你的名字
    版本: 1.5.2 (布林带扩张逻辑升级为上下轨同时扩张)

    策略逻辑:
    - 核心思想: 只在布林带上下轨同时向外扩张（真·开口），且满足趋势和动量条件时入场。
    - 做多: MACD > 0 & MACD金叉 & 布林带真实扩张
    - 做空: MACD < 0 & MACD死叉 & 布林带真实扩张
    """

    # 策略配置
    timeframe = "15m"
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = True
    can_short = True

    # --- 超参数优化 (Hyperopt) 定义 ---
    macd_fast = IntParameter(low=10, high=20, default=12, space="buy", optimize=True)
    macd_slow = IntParameter(low=20, high=35, default=26, space="buy", optimize=True)
    macd_signal = IntParameter(low=8, high=15, default=9, space="buy", optimize=True)
    bb_period = IntParameter(low=15, high=30, default=20, space="buy", optimize=True)
    bb_dev_up = DecimalParameter(
        low=1.8, high=2.5, default=2.0, decimals=1, space="buy", optimize=True
    )

    # 止损和ROI配置
    stoploss = -0.1
    minimal_roi = {"0": 100}

    # 追踪止损配置
    trailing_stop = True
    trailing_stop_positive = 0.03  # 当价格从最高点回落0.3%时，触发追踪止盈
    trailing_stop_positive_offset = 0.15  # 利润达到15%后，才激活追踪止损
    trailing_only_offset_is_reached = True


    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        计算所有需要的技术指标。
        """
        dataframe["zero"] = 0

        # 1. 计算 MACD
        macd_data = ta.MACD(
            dataframe,
            fastperiod=self.macd_fast.value,
            slowperiod=self.macd_slow.value,
            signalperiod=self.macd_signal.value,
        )
        dataframe["macd"] = macd_data["macd"]
        dataframe["macdsignal"] = macd_data["macdsignal"]

        # 2. 计算布林带
        bollinger = qtpylib.bollinger_bands(
            dataframe["close"], window=self.bb_period.value, stds=self.bb_dev_up.value
        )
        dataframe["bb_upperband"] = bollinger["upper"]
        dataframe["bb_middleband"] = bollinger["mid"]
        dataframe["bb_lowerband"] = bollinger["lower"]

        # 3. 【核心修改】计算“真实布林带扩张”信号
        dataframe["bb_real_expansion"] = (
            dataframe["bb_upperband"] > dataframe["bb_upperband"].shift(1)
        ) & (dataframe["bb_lowerband"] < dataframe["bb_lowerband"].shift(1))

        dataframe["rsi_fast"] = ta.RSI(dataframe["close"], timeperiod=4)
        dataframe["rsi_slow"] = ta.RSI(dataframe["close"], timeperiod=20)

        # 4. 计算MACD交叉事件
        dataframe["macd_golden_cross"] = qtpylib.crossed_above(
            # dataframe["macd"], dataframe["macdsignal"]
            dataframe["rsi_fast"], dataframe["rsi_slow"]
        )
        dataframe["macd_death_cross"] = qtpylib.crossed_below(
            # dataframe["macd"], dataframe["macdsignal"]
            dataframe["rsi_fast"], dataframe["rsi_slow"]
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        定义入场信号，使用新的“真实扩张”条件。
        """
        dataframe.loc[
            (
                (dataframe["macd"] > 0)
                & (dataframe["macd_golden_cross"])
                & (dataframe["bb_real_expansion"])  # 使用新信号
            ),
            "enter_long",
        ] = 1

        dataframe.loc[
            (
                (dataframe["macd"] < 0)
                & (dataframe["macd_death_cross"])
                & (dataframe["bb_real_expansion"])  # 使用新信号
            ),
            "enter_short",
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        定义出场信号，同样使用新的“真实扩张”条件。
        """

        dataframe.loc[
            ((dataframe["macd"] < 0) & (dataframe["macd_death_cross"])),
            "exit_long",
        ] = 1

        dataframe.loc[
            ((dataframe["macd"] > 0) & (dataframe["macd_golden_cross"])),
            "exit_short",
        ] = 1

        return dataframe
